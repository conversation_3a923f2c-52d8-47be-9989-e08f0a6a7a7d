"""
分布式采样器

提供支持 DDP 的采样策略，包括变长样本的分布式 batch sampler。
"""

import math
import numpy as np
from typing import List, Iterator, Optional
from torch.utils.data import Sampler


class DistributedSingleIndexBatchSampler(Sampler[List[int]]):
    """
    适用于"一批仅含一个样本索引"的场景（如 DaySection：每天一个 batch）。
    - 将 [0..num_samples-1] 在多卡间切分，yield [[idx0], [idx1], ...]
    - 支持 shuffle（每个 epoch 重排），支持 drop_last（对齐批量）
    """
    def __init__(
        self,
        num_samples: int,
        num_replicas: Optional[int] = None,
        rank: Optional[int] = None,
        shuffle: bool = True,
        drop_last: bool = False,
        seed: int = 42,
    ):
        if num_replicas is None:
            import torch.distributed as dist
            if not dist.is_available() or not dist.is_initialized():
                raise RuntimeError("DDP 未初始化，需传入 num_replicas/rank 或先 init_process_group")
            num_replicas = dist.get_world_size()
            rank = dist.get_rank()
        
        self.num_samples_total = int(num_samples)
        self.num_replicas = int(num_replicas)
        self.rank = int(rank)
        self.shuffle = shuffle
        self.drop_last = drop_last
        self.seed = seed
        
        # 计算每卡样本数
        if self.drop_last and self.num_samples_total % self.num_replicas != 0:
            self.num_samples = self.num_samples_total // self.num_replicas
        else:
            self.num_samples = int(math.ceil(self.num_samples_total / self.num_replicas))
        
        self.total_size = self.num_samples * self.num_replicas
        self.epoch = 0

    def __iter__(self):
        # 构造全局索引序列
        g = np.arange(self.num_samples_total)
        if self.shuffle:
            rng = np.random.default_rng(self.seed + self.epoch)
            rng.shuffle(g)
        
        # 如需对齐，做 padding
        if not self.drop_last:
            if self.total_size > self.num_samples_total:
                # 使用模运算来循环填充，避免简单重复前几个元素
                pad = self.total_size - self.num_samples_total
                pad_indices = g[np.arange(pad) % len(g)]
                g = np.concatenate([g, pad_indices])
        else:
            g = g[:self.total_size]
        
        # 切片到各 rank
        indices = g[self.rank:self.total_size:self.num_replicas]
        
        # 生成 batch（单样本一批）
        for idx in indices:
            yield [int(idx)]

    def __len__(self):
        return self.num_samples

    def set_epoch(self, epoch: int):
        """设置 epoch 用于控制 shuffle 的随机种子"""
        self.epoch = int(epoch)
