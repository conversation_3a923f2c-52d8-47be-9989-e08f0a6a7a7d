import torch
import torch.nn as nn
from typing import Optional, Literal

import torch
import torch.nn as nn
from typing import Optional, Literal


# ---------- 基础模块 ----------
class FeatureProj(nn.Module):
    def __init__(self, input_dim: int, hidden_dim: int, hidden: Optional[int] = None, drop: float = 0.0):
        super().__init__()
        hidden = hidden or max(hidden_dim, input_dim)
        self.net = nn.Sequential(
            nn.Linear(input_dim, hidden), nn.GELU(), nn.Dropout(drop),
            nn.Linear(hidden, hidden_dim),
        )

    def forward(self, x):  # x: [S,T,F] -> [S,T,d]
        S, T, F = x.shape
        return self.net(x.reshape(S*T, F)).reshape(S, T, -1)


class AxialPos2D(nn.Module):
    """2D 可学习位置：stock_pos[s] + time_pos[t]"""
    def __init__(self, hidden_dim: int, max_stocks=8192, max_time=1024):
        super().__init__()
        self.stock_pos = nn.Parameter(torch.empty(max_stocks, hidden_dim))
        self.time_pos  = nn.Parameter(torch.empty(max_time,   hidden_dim))
        nn.init.xavier_uniform_(self.stock_pos)
        nn.init.xavier_uniform_(self.time_pos)

    def forward(self, x):   # x: [S,T,d]
        S, T, d = x.shape
        return x + self.stock_pos[:S, None, :] + self.time_pos[None, :T, :]


def make_encoder_layer(hidden_dim, nhead, dim_ff, drop, batch_first):
    return nn.TransformerEncoderLayer(
        hidden_dim, nhead, dim_feedforward=dim_ff, dropout=drop,
        activation="gelu", batch_first=batch_first, norm_first=True
    )


# ---------- 模型 ----------
class TimeFirstModel(nn.Module):
    """
    输入: x [S,T,F]；输出: [S,1]
    流程：特征投影+2D位置 -> 时间轴Encoder -> 池化(T) -> (可选)股票轴Encoder -> MLP
    """
    def __init__(self,
                 input_dim=9, hidden_dim=64,
                 time_nhead=4, time_layers=2,
                 stock_nhead=4, stock_layers=1,      # 设为0即不做股票轴注意力
                 dim_ff=256, dropout=0.1,
                 max_stocks=8192, max_time=1024,
                 output_dim=1,
                 pool: Literal["mean","attn"]="mean"):
        super().__init__()
        self.proj  = FeatureProj(input_dim, hidden_dim, drop=dropout)
        self.pos2d = AxialPos2D(hidden_dim, max_stocks=max_stocks, max_time=max_time)

        time_layer = make_encoder_layer(hidden_dim, time_nhead, dim_ff, dropout, batch_first=False)
        self.time_encoder = nn.TransformerEncoder(time_layer, num_layers=time_layers)

        self.pool_type = pool
        if pool == "attn":
            self.pool_attn = nn.Linear(hidden_dim, 1)

        self.use_stock_encoder = stock_layers > 0
        if self.use_stock_encoder:
            stock_layer = make_encoder_layer(hidden_dim, stock_nhead, dim_ff, dropout, batch_first=True)
            self.stock_encoder = nn.TransformerEncoder(stock_layer, num_layers=stock_layers)

        self.head = nn.Sequential(
            nn.LayerNorm(hidden_dim),
            nn.Linear(hidden_dim, hidden_dim), nn.GELU(), nn.Dropout(dropout),
            nn.Linear(hidden_dim, output_dim),
        )

    def _time_pool(self, h_ts):  # h_ts: [T,B,d] -> [B,d]
        if self.pool_type == "mean":
            return h_ts.mean(dim=0)
        w = torch.softmax(self.pool_attn(h_ts), dim=0)  # [T,B,1]
        return (w * h_ts).sum(dim=0)

    def forward(self, x):
        assert x.dim() == 3, "x must be [S,T,F]"
        # [S,T,F] -> [S,T,d] + 2D pos
        x = self.proj(x)
        x = self.pos2d(x)

        # 时间轴注意力（把 S 当 batch，一次性全部处理）
        tb = x.transpose(0, 1).contiguous()    # [T,S,d] (batch_first=False，B=S)
        h  = self.time_encoder(tb)             # [T,S,d]
        hS = self._time_pool(h)                # [S,d]

        # （可选）股票轴注意力：把 S 当序列长度
        if self.use_stock_encoder:
            hS = self.stock_encoder(hS.unsqueeze(0)).squeeze(0)  # [S,d]

        return self.head(hS)  # [S,1]


class TimeOnlyModel(nn.Module):
    """只做时间轴注意力 -> 池化 -> MLP；输入 [S,T,F]，输出 [S,1]。"""
    def __init__(self, **kw):
        super().__init__()
        self.core = TimeFirstModel(
            input_dim=kw.get("input_dim", 9),
            hidden_dim=kw.get("hidden_dim", 64),
            time_nhead=kw.get("time_nhead", 4),
            time_layers=kw.get("time_layers", 2),
            stock_nhead=1, stock_layers=0,        # 禁用股票轴注意力
            dim_ff=kw.get("dim_ff", 256),
            dropout=kw.get("dropout", 0.1),
            max_stocks=kw.get("max_stocks", 8192),
            max_time=kw.get("max_time", 1024),
            pool=kw.get("pool", "mean"),
            output_dim=kw.get("output_dim", 1)
        )

    def forward(self, x):
        return self.core(x)

if __name__ == "__main__":
    device = torch.device("cuda")
    torch.cuda.reset_peak_memory_stats(device)

    S,T,F = 5000,90,7
    x = torch.randn(S,T,F, device=device)
    model = TimeFirstModel(input_dim=F, hidden_dim=256, output_dim=1, 
                        time_layers=1, time_nhead=4,
                        stock_layers=1, stock_nhead=4,
                        dropout=0.1).to(device)


    # 推理显存
    with torch.inference_mode():
        _ = model(x)
    torch.cuda.synchronize()
    infer_peak = torch.cuda.max_memory_allocated(device) / 1024**2
    print(f"Inference peak: {infer_peak:.1f} MB")

    # 训练显存（前向+反向）
    torch.cuda.reset_peak_memory_stats(device)
    x = torch.randn(S,T,F, device=device)
    y = model(x)
    loss = y.float().mean()
    loss.backward()
    torch.cuda.synchronize()
    train_peak = torch.cuda.max_memory_allocated(device) / 1024**2
    print(f"Train peak: {train_peak:.1f} MB  (~{train_peak/infer_peak:.2f}x of inference)")