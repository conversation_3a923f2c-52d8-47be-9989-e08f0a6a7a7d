"""
测试TensorDaySectionDataset加载 - 单卡版本

目的：验证TensorDaySectionDataset能否正常遍历所有batch
"""

import os
import sys
import argparse
import numpy as np
import torch
from loguru import logger
import time
from tqdm import tqdm

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from DLlib.data.cube import MinuteCube
from DLlib.data.field_spec import FieldSpec
from DLlib.data.datasets import TensorDaySectionDataset
from DLlib.data.collate import passthrough_collate_dict


def test_dataset_loading(data_path: str):
    """测试数据集加载"""
    logger.info("=== TensorDaySectionDataset 单卡测试 ===")
    
    # 配置参数
    n_feat = 97
    n_label = 1
    n_weight = 0
    fillna_features_to_zero = True
    features_norm = "cs"
    labels_norm = "cs"
    num_workers = 4
    
    # 加载数据
    logger.info(f"加载数据: {data_path}")
    X = np.load(data_path, mmap_mode="r")  # [F, D, T, N]
    F, D, T, N = X.shape
    logger.info(f"数据形状: F={F}, D={D}, T={T}, N={N}")
    
    # 取一个小的子集进行测试（前10天）
    test_days = min(10, D)
    X_test = X[:, :test_days, :, :]
    logger.info(f"测试子集: 前{test_days}天")
    
    # 构造cube
    logger.info("构造MinuteCube...")
    cube = MinuteCube.from_fdtN(X_test)
    logger.info(f"Cube形状: D={cube.D}, T={cube.T}, N={cube.N}, F={cube.F}")
    
    # 创建FieldSpec
    spec = FieldSpec(n_feat=n_feat, n_label=n_label, n_weight=n_weight)
    
    # 创建数据集
    logger.info("创建TensorDaySectionDataset...")
    dataset = TensorDaySectionDataset(
        cube, spec,
        fillna_features_to_zero=fillna_features_to_zero,
        features_norm=features_norm, 
        labels_norm=labels_norm
    )
    
    logger.info(f"数据集长度: {len(dataset)}")
    
    # 创建DataLoader
    logger.info("创建DataLoader...")
    dataloader = torch.utils.data.DataLoader(
        dataset, 
        batch_size=1,  # 单卡版本，batch_size=1
        shuffle=False, 
        collate_fn=passthrough_collate_dict,
        num_workers=num_workers, 
        pin_memory=True
    )
    
    # 测试遍历
    logger.info("开始遍历DataLoader...")
    start_time = time.time()
    
    try:
        for i, batch in enumerate(tqdm(dataloader, desc="遍历batch")):
            # 检查batch内容
            features = batch["features"]
            labels = batch["label"]
            weights = batch["weight"]
            day_idx = batch["day_idx"]
            n_idx = batch["n_idx"]
            
            # 打印第一个batch的信息
            if i == 0:
                logger.info(f"第一个batch信息:")
                logger.info(f"  features shape: {features.shape}")
                logger.info(f"  labels shape: {labels.shape}")
                logger.info(f"  weights: {weights}")
                logger.info(f"  day_idx: {day_idx}")
                logger.info(f"  n_idx shape: {n_idx.shape}")
                logger.info(f"  features dtype: {features.dtype}")
                logger.info(f"  labels dtype: {labels.dtype}")
                
                # 检查是否有NaN或Inf
                features_has_nan = torch.isnan(features).any()
                features_has_inf = torch.isinf(features).any()
                labels_has_nan = torch.isnan(labels).any()
                labels_has_inf = torch.isinf(labels).any()
                
                logger.info(f"  features有NaN: {features_has_nan}")
                logger.info(f"  features有Inf: {features_has_inf}")
                logger.info(f"  labels有NaN: {labels_has_nan}")
                logger.info(f"  labels有Inf: {labels_has_inf}")
            
            # 每5个batch打印一次进度
            if (i + 1) % 5 == 0:
                logger.info(f"已处理 {i + 1}/{len(dataloader)} batches")
        
        end_time = time.time()
        logger.info(f"✅ 成功遍历完所有 {len(dataloader)} 个batches")
        logger.info(f"总耗时: {end_time - start_time:.2f}秒")
        
    except Exception as e:
        logger.error(f"❌ 遍历过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="测试TensorDaySectionDataset - 单卡版本")
    parser.add_argument("--data_path", type=str, 
                       default="/disk4/shared/intern/laiyc/forModel/alpha20.npy",
                       help="数据文件路径")
    args = parser.parse_args()
    
    success = test_dataset_loading(args.data_path)
    if success:
        logger.info("🎉 测试通过！")
    else:
        logger.error("💥 测试失败！")
        sys.exit(1)
