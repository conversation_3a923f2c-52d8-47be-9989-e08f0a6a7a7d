"""
Tree Pipeline - 树模型端到端训练

特点:
- 直接从X[F,D,T,N]构建表格样本，无需特征标准化
- 对y进行截面zscore处理（沿N轴），保留NaN
- 支持超大数据的memmap处理
- 使用Optuna进行超参优化
- 滚动训练和测试
- W&B记录实验
"""

import os, time, argparse, numpy as np
from dataclasses import dataclass
from loguru import logger
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from MLlib.utils.rolling import rolling_splits_by_years
from MLlib.utils.metrics import rank_ic_focus
from MLlib.models.lgbm_trainer import train_lgbm_one_fold, optuna_search, save_params_json
from MLlib.data.tree_dataset import TreeCubeDataset
from DLlib.train.wandb_tracker import WandbTracker
from DLlib.data.field_spec import FieldSpec  # 只为取 n_feat/n_label/n_weight
from DLlib.data.ddp_utils import set_global_seed


@dataclass
class TreeConfig:
    """树模型Pipeline配置"""
    # 实验名称
    exp_name: str = f"tree_{time.strftime('%m%d_%H%M%S')}"
    
    # 数据配置
    data_path: str = "/disk4/shared/intern/laiyc/forModel/alpha20.npy"
    
    # FieldSpec配置
    n_feat: int = 20
    n_label: int = 1 
    n_weight: int = 0

    # 滚动配置
    train_years: float = 3.0
    test_years: float = 1.0
    days_per_year: int = 240
    inner_val_ratio: float = 0.2

    # Optuna搜参配置
    n_trials: int = 50
    seed: int = 666

    # LGB基础参数（固定学习率等；搜索其余）
    base_params: dict = None
    early_stopping_rounds: int = 200
    verbose_eval: int = 100

    # 关注的分钟索引
    T_focus = [30, 60, 90, 120, 150, 180, 210]

    # 输出目录
    ckpt_root: str = "projs/stock1m/checkpoints_tree"
    work_root: str = "projs/stock1m/work_tree"  # memmap/中间文件, 暂时没用

    # W&B配置
    wandb_project: str = "tslib"
    wandb_group: str = "stock1m_tree"
    wandb_mode: str = "online"
    wandb_dir: str = "projs/stock1m/logs/wandb"
    wandb_tags: list = None

    def __post_init__(self):
        if self.base_params is None:
            self.base_params = {
                "objective": "regression",
                "metric": None,            # 用自定义 IC 早停
                "boosting_type": "gbdt",
                "learning_rate": 0.02,     # 固定学习率，每个任务proj手调
                "n_estimators": 20000,
                "device": "cpu",           # 使用 CPU 多核
                "n_jobs": os.cpu_count() or 88,
                "verbose": -1,
                "seed": self.seed,         # 设置LightGBM随机种子
                "feature_fraction_seed": self.seed,  # 特征采样种子
                "bagging_seed": self.seed,           # bagging种子
                "data_random_seed": self.seed,       # 数据随机种子
            }
        if self.wandb_tags is None:
            self.wandb_tags = ["Tree", "LightGBM", "IC-focus", "CPU"]


def run(cfg: TreeConfig):
    """运行树模型Pipeline"""

    # 设置全局随机种子
    set_global_seed(cfg.seed)

    # 1) 读取超大 X：mmap模式，不占内存
    logger.info(f"=== Tree Pipeline ===")
    logger.info(f"正在加载特征数据: {cfg.data_path}")
    X = np.load(cfg.data_path, mmap_mode="r")   # [F,D,T,N]，按需访问
    F, D, T, N = X.shape
    
    spec = FieldSpec(n_feat=cfg.n_feat, n_label=cfg.n_label, n_weight=cfg.n_weight)
    assert spec.n_feat + spec.n_weight + spec.n_label == F, f"特征维度不匹配: {spec.n_feat}+{spec.n_weight}+{spec.n_label} != {F}"

    logger.info(f"最终数据形状: F={F}, D={D}, T={T}, N={N}")
    logger.info(f"特征配置: n_feat={cfg.n_feat}, n_label={cfg.n_label}, n_weight={cfg.n_weight}")

    # 2) 生成滚动切分
    folds = rolling_splits_by_years(
        D=D, 
        train_years=cfg.train_years, 
        test_years=cfg.test_years,
        days_per_year=cfg.days_per_year, 
        inner_val_ratio=cfg.inner_val_ratio,
    )
    logger.info(f"滚动配置: {cfg.train_years}年训练 + {cfg.test_years}年测试")
    logger.info(f"总计 {len(folds)} 个折")

    # 3) 初始化W&B跟踪器（单进程）
    tracker = WandbTracker(
        project=cfg.wandb_project,
        run_name=f"{cfg.exp_name}-LGBM-{cfg.train_years}x{cfg.test_years}",
        group=cfg.wandb_group,
        mode=cfg.wandb_mode,
        dir_=cfg.wandb_dir,
        tags=cfg.wandb_tags,
        config={
            "pipeline": "Tree",
            "model": "LightGBM",
            "base_params": cfg.base_params,
            "T_focus": cfg.T_focus,
            "optuna": {
                "n_trials": cfg.n_trials,
                "seed": cfg.seed,
            },
            "rolling": f"{cfg.train_years}x{cfg.test_years} years, val_ratio={cfg.inner_val_ratio}",
            "data_shape": [F, D, T, N],
            "num_folds": len(folds),
        },
    )

    # 创建输出目录
    os.makedirs(cfg.ckpt_root, exist_ok=True)

    # 4) 第一个训练窗口上做 Optuna 超参搜索
    logger.info("[Phase 1] 开始Optuna超参搜索...")
    idx_tr, idx_va, _ = folds[0]

    logger.info(f"Optuna窗口: 训练{len(idx_tr)}天, 验证{len(idx_va)}天")

    # 构建扁平样本，利用mmap的按需加载特性
    logger.info("构建训练和验证样本...")
    dataset = TreeCubeDataset(
        X,
        n_feat=spec.n_feat,
        n_weight=spec.n_weight,
        n_label=spec.n_label
    )

    tr, va, _ = dataset.split_and_flatten(
        idx_tr=idx_tr,
        idx_va=idx_va,
        idx_te=[],  # Optuna阶段不需要测试集
        drop_nan_y=True
    )

    logger.info(f"训练样本数: {tr['X'].shape[0]}, 验证样本数: {va['X'].shape[0]}")

    # 开始Optuna搜参
    best_params = optuna_search(
        tr, va,
        base_params=cfg.base_params,
        n_trials=cfg.n_trials,
        seed=cfg.seed,
        T_focus=cfg.T_focus,
        direction="maximize",
    )
    
    # 保存最优参数
    params_path = os.path.join(cfg.ckpt_root, "best_params.json")
    save_params_json(best_params, params_path)
    
    # 记录到W&B
    tracker.log_metrics({"optuna/n_trials": cfg.n_trials})
    tracker.save_artifact(params_path, type_="config")
    
    logger.info(f"超参搜索完成，最优参数已保存到: {params_path}")

    # 5) 固定最优参数，滚动训练所有折并测试
    logger.info("[Phase 2] 开始滚动训练...")
    all_valid_ic, all_test_ic = [], []
    
    for fid, (idx_tr, idx_va, idx_te) in enumerate(folds):
        logger.info(f"[Fold {fid}] 开始训练")
        logger.info(f"  训练: {len(idx_tr)}天, 验证: {len(idx_va)}天, 测试: {len(idx_te)}天")
        
        fold_dir = os.path.join(cfg.ckpt_root, f"fold{fid:02d}")
        os.makedirs(fold_dir, exist_ok=True)

        # 构建训练、验证、测试数据
        dataset = TreeCubeDataset(
            X,
            n_feat=spec.n_feat,
            n_weight=spec.n_weight,
            n_label=spec.n_label
        )

        tr, va, te = dataset.split_and_flatten(
            idx_tr=idx_tr,
            idx_va=idx_va,
            idx_te=idx_te,
            drop_nan_y=True
        )

        logger.info(f"  样本数 - 训练: {tr['X'].shape[0]}, 验证: {va['X'].shape[0]}, 测试: {te['X'].shape[0]}")

        # 训练模型
        booster, va_metrics = train_lgbm_one_fold(
            tr, va, 
            lgb_params=best_params,
            early_stopping_rounds=cfg.early_stopping_rounds, 
            verbose_eval=cfg.verbose_eval,
            T_focus=cfg.T_focus
        )
        
        va_ic = va_metrics["valid/ic_focus"]
        all_valid_ic.append(va_ic)
        
        # 测试
        te_pred = booster.predict(te["X"], num_iteration=booster.best_iteration)
        te_ic = rank_ic_focus(te_pred, te["y"], te["d"], te["t"], T_focus=cfg.T_focus)
        all_test_ic.append(te_ic)
        
        # 记录单个fold结果 - 使用简化方式
        tracker.log_metrics({f"fold_{fid}": te_ic})

        # 保存模型和预测结果
        model_path = os.path.join(fold_dir, "lgbm.txt")
        booster.save_model(model_path)
        
        pred_path = os.path.join(fold_dir, "test_pred.npy")
        label_path = os.path.join(fold_dir, "test_y.npy")
        index_path = os.path.join(fold_dir, "test_dt.npy")
        
        np.save(pred_path, te_pred.astype(np.float32))
        np.save(label_path, te["y"].astype(np.float32))
        np.save(index_path, np.stack([te["d"], te["t"]], axis=1))
        
        # 保存到W&B
        tracker.save_artifact(model_path, type_="model")
        tracker.save_artifact(pred_path, type_="predictions")
        
        logger.info(f"[Fold {fid}] 完成 - 验证IC: {va_ic:.6f}, 测试IC: {te_ic:.6f}")

    # 6) 汇总结果
    if len(all_test_ic):
        cv_mean = float(np.mean(all_test_ic))
        cv_std = float(np.std(all_test_ic))
        
        tracker.log_metrics({
            "cv_mean": cv_mean,
            "cv_std": cv_std,
            "cv_folds": len(folds)
        })
        
        logger.info("=== 最终结果汇总 ===")
        logger.info(f"CV总结 - 平均测试IC: {cv_mean:.6f} ± {cv_std:.6f}")
        logger.info(f"总计 {len(folds)} 个折")

    # 结束实验
    tracker.finish()
    logger.info("Tree Pipeline 完成!")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Tree Pipeline")
    parser.add_argument("--data_path", type=str, 
                       default="projs/stock1m/data/OHLCVA_Vwap_cube.npy",
                       help="数据文件路径")
    parser.add_argument("--exp_name", type=str, 
                       default=f"tree_{time.strftime('%m%d_%H%M%S')}",
                       help="实验名称")
    parser.add_argument("--n_trials", type=int, default=50,
                       help="Optuna搜参试验次数")
    parser.add_argument("--wandb_mode", type=str, default="online",
                       choices=["online", "offline"],
                       help="W&B模式")
    args = parser.parse_args()
    
    # 创建配置
    cfg = TreeConfig(
        data_path=args.data_path,
        exp_name=args.exp_name,
        n_trials=args.n_trials,
        wandb_mode=args.wandb_mode,
    )
    
    # 运行pipeline
    run(cfg)


if __name__ == "__main__":
    main()