#!/bin/bash

# DDP Pipeline 启动脚本
# 支持启动三种pipeline: day, pair, minute

set -e

# 默认配置
NPROC=${NPROC:-8}
PIPELINE=${PIPELINE:-day}
DATA_PATH=${DATA_PATH:-"/disk4/shared/intern/laiyc/forModel/alpha20.npy"}
MASTER_PORT=${MASTER_PORT:-29500}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --pipeline)
            PIPELINE="$2"
            shift 2
            ;;
        --nproc)
            NPROC="$2"
            shift 2
            ;;
        --data_path)
            DATA_PATH="$2"
            shift 2
            ;;
        --master_port)
            MASTER_PORT="$2"
            shift 2
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --pipeline PIPELINE    Pipeline类型 (day|pair|minute), 默认: day"
            echo "  --nproc NPROC          GPU数量, 默认: 8"
            echo "  --data_path PATH       数据文件路径, 默认: projs/stock1m/data/OHLCVA_Vwap_cube.npy"
            echo "  --master_port PORT     主端口, 默认: 29500"
            echo "  --help, -h             显示此帮助信息"
            echo ""
            echo "Pipeline说明:"
            echo "  day     - DaySection Pipeline (全截面分析)"
            echo "  pair    - Pair Pipeline (个股时间序列)"
            echo "  minute  - MinuteAcross Pipeline (跨天回看预测)"
            echo ""
            echo "示例:"
            echo "  $0 --pipeline day --nproc 8"
            echo "  $0 --pipeline pair --nproc 4"
            echo "  $0 --pipeline minute --nproc 8 --data_path /path/to/data.npy"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 验证pipeline类型并确定目标脚本
case $PIPELINE in
    day)
        TARGET_SCRIPT="projs.stock1m.scripts.pipelines.day_section"
        DESCRIPTION="DaySection Pipeline - 全截面分析"
        ;;
    pair)
        TARGET_SCRIPT="projs.stock1m.scripts.pipelines.pair"
        DESCRIPTION="Pair Pipeline - 个股时间序列分析"
        ;;
    minute)
        TARGET_SCRIPT="projs.stock1m.scripts.pipelines.minute_across"
        DESCRIPTION="MinuteAcross Pipeline - 跨天回看预测"
        ;;
    *)
        echo "错误: 无效的pipeline类型 '$PIPELINE'"
        echo "支持的类型: day, pair, minute"
        exit 1
        ;;
esac

# 验证数据文件
if [[ ! -f "$DATA_PATH" ]]; then
    echo "错误: 数据文件不存在: $DATA_PATH"
    exit 1
fi


# 设置环境变量
export CUDA_VISIBLE_DEVICES=$(seq -s, 0 $((NPROC-1)))
# 关闭NCCL调试信息和警告
export NCCL_DEBUG=WARN
export NCCL_IGNORE_DISABLED_P2P=1
export NCCL_IGNORE_CPU_AFFINITY=1
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 设置wandb token（如果存在）
if [[ -n "${WANDB_API_KEY}" ]]; then
    echo "✅ WandB API Key已设置"
else
    echo "⚠️  WandB API Key未设置，将使用离线模式"
fi

# 启动训练
echo "🎯 开始启动 $PIPELINE pipeline..."
echo "-" * 60

torchrun \
    --nproc_per_node=$NPROC \
    --master_port=$MASTER_PORT \
    -m $TARGET_SCRIPT \
    --data_path "$DATA_PATH"

TRAIN_EXIT_CODE=$?

if [ $TRAIN_EXIT_CODE -eq 0 ]; then
    echo "✅ Pipeline执行成功完成"
else
    echo "❌ Pipeline执行失败，退出码: $TRAIN_EXIT_CODE"
fi


echo "Pipeline脚本结束"
exit $TRAIN_EXIT_CODE
