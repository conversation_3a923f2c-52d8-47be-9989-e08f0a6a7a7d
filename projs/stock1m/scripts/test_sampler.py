"""
测试DistributedSingleIndexBatchSampler的行为
"""

import os
import sys
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from DLlib.data.samplers import DistributedSingleIndexBatchSampler


def test_sampler_behavior():
    """测试采样器在不同配置下的行为"""
    
    print("=== 测试DistributedSingleIndexBatchSampler ===")
    
    # 测试场景1：10个样本，2个GPU，不drop_last
    print("\n场景1: 10个样本，2个GPU，drop_last=False")
    num_samples = 10
    num_replicas = 2
    
    for rank in range(num_replicas):
        sampler = DistributedSingleIndexBatchSampler(
            num_samples=num_samples,
            num_replicas=num_replicas,
            rank=rank,
            shuffle=False,
            drop_last=False
        )
        
        indices = list(sampler)
        print(f"  Rank {rank}: {len(indices)} batches, indices: {[batch[0] for batch in indices]}")
        print(f"    total_size: {sampler.total_size}, num_samples: {sampler.num_samples}")
    
    # 测试场景2：10个样本，2个GPU，drop_last
    print("\n场景2: 10个样本，2个GPU，drop_last=True")
    for rank in range(num_replicas):
        sampler = DistributedSingleIndexBatchSampler(
            num_samples=num_samples,
            num_replicas=num_replicas,
            rank=rank,
            shuffle=False,
            drop_last=True
        )
        
        indices = list(sampler)
        print(f"  Rank {rank}: {len(indices)} batches, indices: {[batch[0] for batch in indices]}")
        print(f"    total_size: {sampler.total_size}, num_samples: {sampler.num_samples}")
    
    # 测试场景3：9个样本，2个GPU，不drop_last（奇数样本）
    print("\n场景3: 9个样本，2个GPU，drop_last=False")
    num_samples = 9
    for rank in range(num_replicas):
        sampler = DistributedSingleIndexBatchSampler(
            num_samples=num_samples,
            num_replicas=num_replicas,
            rank=rank,
            shuffle=False,
            drop_last=False
        )
        
        indices = list(sampler)
        print(f"  Rank {rank}: {len(indices)} batches, indices: {[batch[0] for batch in indices]}")
        print(f"    total_size: {sampler.total_size}, num_samples: {sampler.num_samples}")
    
    # 测试场景4：9个样本，2个GPU，drop_last（奇数样本）
    print("\n场景4: 9个样本，2个GPU，drop_last=True")
    for rank in range(num_replicas):
        sampler = DistributedSingleIndexBatchSampler(
            num_samples=num_samples,
            num_replicas=num_replicas,
            rank=rank,
            shuffle=False,
            drop_last=True
        )
        
        indices = list(sampler)
        print(f"  Rank {rank}: {len(indices)} batches, indices: {[batch[0] for batch in indices]}")
        print(f"    total_size: {sampler.total_size}, num_samples: {sampler.num_samples}")
    
    # 测试场景5：3个GPU的情况
    print("\n场景5: 10个样本，3个GPU，drop_last=False")
    num_samples = 10
    num_replicas = 3
    for rank in range(num_replicas):
        sampler = DistributedSingleIndexBatchSampler(
            num_samples=num_samples,
            num_replicas=num_replicas,
            rank=rank,
            shuffle=False,
            drop_last=False
        )
        
        indices = list(sampler)
        print(f"  Rank {rank}: {len(indices)} batches, indices: {[batch[0] for batch in indices]}")
        print(f"    total_size: {sampler.total_size}, num_samples: {sampler.num_samples}")
    
    # 检查是否有重复或遗漏
    print("\n=== 检查覆盖情况 ===")
    
    # 场景1检查
    print("场景1覆盖检查:")
    all_indices = []
    num_samples = 10
    num_replicas = 2
    for rank in range(num_replicas):
        sampler = DistributedSingleIndexBatchSampler(
            num_samples=num_samples,
            num_replicas=num_replicas,
            rank=rank,
            shuffle=False,
            drop_last=False
        )
        indices = [batch[0] for batch in sampler]
        all_indices.extend(indices)
    
    print(f"  所有索引: {sorted(all_indices)}")
    print(f"  原始范围: {list(range(num_samples))}")
    print(f"  重复索引: {[x for x in set(all_indices) if all_indices.count(x) > 1]}")
    missing = [x for x in range(num_samples) if x not in all_indices]
    print(f"  遗漏索引: {missing}")


if __name__ == "__main__":
    test_sampler_behavior()
