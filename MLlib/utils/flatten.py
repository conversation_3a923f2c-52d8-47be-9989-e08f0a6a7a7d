# MLlib/utils/flatten.py
from __future__ import annotations
import numpy as np

def _safe_std(std: np.ndarray, eps: float) -> np.ndarray:
    """安全的标准差计算，避免除零和非有限值"""
    return np.where((~np.isfinite(std)) | (std < eps), eps, std)

def zscore_y_cs(y: np.ndarray, eps: float = 1e-6) -> np.ndarray:
    """
    标签做截面 zscore（沿 N），用 nanmean/nanstd；原始 NaN 保留为 NaN。
      适配 y 为 [N, T, d_y] 或 [N, 1, d_y]
    """
    mask = ~np.isfinite(y)                              # True 表示原先是 NaN/Inf
    mu = np.nanmean(y, axis=0, keepdims=True)
    sd = np.nanstd (y, axis=0, keepdims=True)
    sd = _safe_std(sd, eps)
    out = (y - mu) / sd
    out[mask] = np.nan
    return out



