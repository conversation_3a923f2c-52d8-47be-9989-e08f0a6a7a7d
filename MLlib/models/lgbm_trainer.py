# MLlib/models/lgbm_trainer.py
from __future__ import annotations
import os, json, numpy as np, lightgbm as lgb, optuna
from typing import Dict, Sequence, Tuple, Optional
from loguru import logger
from optuna.samplers import TPESampler
from MLlib.utils.metrics import rank_ic_focus

def train_lgbm_one_fold(
    train: Dict[str, np.ndarray],
    valid: Dict[str, np.ndarray],
    *,
    lgb_params: Dict,
    early_stopping_rounds: int = 200,
    verbose_eval: int = 100,
    T_focus: Sequence[int] = (30,60,90,120,150,180,210,230),
) -> Tuple[lgb.Booster, Dict[str, float]]:
    """
    训练单个LightGBM模型
    
    参数:
        train: 训练数据 {"X": features, "y": labels, "d": days, "t": minutes, "n": stocks}
        valid: 验证数据，格式同上
        lgb_params: LightGBM参数
        early_stopping_rounds: 早停轮数
        verbose_eval: 日志输出频率
        T_focus: 关注的分钟索引，用于计算IC
        
    返回:
        (训练好的booster, 验证集指标)
    """
    # 创建LightGBM数据集
    lgb_train = lgb.Dataset(train["X"], label=train["y"], free_raw_data=False)
    lgb_valid = lgb.Dataset(valid["X"], label=valid["y"], reference=lgb_train, free_raw_data=False)

    # 准备验证集数据用于IC计算
    va_d, va_t, va_y, va_n = valid["d"], valid["t"], valid["y"], valid["n"]
    
    def feval_ic(preds: np.ndarray, data: lgb.Dataset):
        """自定义评估函数：计算rank IC focus"""
        ic = rank_ic_focus(preds, va_y, va_d, va_t, va_n, T_focus=T_focus)
        return "rank_ic_focus", ic, True  # (eval_name, eval_result, is_higher_better)

    # 训练模型
    booster = lgb.train(
        params=lgb_params,
        train_set=lgb_train,
        valid_sets=[lgb_valid],
        feval=feval_ic if (lgb_params.get("metric", None) in (None, "None")) else None,
        callbacks=[
            lgb.early_stopping(stopping_rounds=early_stopping_rounds, verbose=True),
            lgb.log_evaluation(period=verbose_eval),
        ],
    )
    
    # 计算验证集指标
    va_pred = booster.predict(valid["X"], num_iteration=booster.best_iteration)
    va_mse = float(np.mean((va_pred - valid["y"])**2))
    va_ic = rank_ic_focus(va_pred, valid["y"], valid["d"], valid["t"], T_focus=T_focus)
    
    return booster, {"valid/mse": va_mse, "valid/ic_focus": va_ic}

def optuna_search(
    train: Dict[str, np.ndarray],
    valid: Dict[str, np.ndarray],
    *,
    base_params: Dict,
    n_trials: int = 50,
    seed: int = 666,
    T_focus: Sequence[int] = (30,60,90,120,150,180,210,230),
    direction: str = "maximize",  # 以 IC 为目标
) -> Dict:
    """
    使用Optuna进行超参数搜索
    
    参数:
        train: 训练数据
        valid: 验证数据
        base_params: 基础参数（固定的参数，如学习率等）
        n_trials: 搜索试验次数
        seed: 随机种子
        T_focus: 关注的分钟索引
        direction: 优化方向，"maximize"表示最大化IC
        
    返回:
        最优参数字典
    """
    def objective(trial: optuna.Trial) -> float:
        """Optuna优化目标函数"""
        params = dict(base_params)
        
        # 搜索空间定义
        params.update({
            "lambda_l1": trial.suggest_float("lambda_l1", 1e-8, 100.0, log=False),
            "lambda_l2": trial.suggest_float("lambda_l2", 1e-8, 100.0, log=False),
            "num_leaves": trial.suggest_int("num_leaves", 2, 512),
            "max_depth": trial.suggest_int("max_depth", 3, 15),
            "feature_fraction": trial.suggest_float("feature_fraction", 0.1, 1.0),
            "bagging_fraction": trial.suggest_float("bagging_fraction", 0.1, 1.0),
            "bagging_freq": trial.suggest_int("bagging_freq", 1, 10),
            "min_child_samples": trial.suggest_int("min_child_samples", 5, 100),
            "min_split_gain": trial.suggest_float("min_split_gain", 1e-10, 1e-2, log=True),
        })
        
        try:
            booster, metrics = train_lgbm_one_fold(
                train, valid, 
                lgb_params=params, 
                early_stopping_rounds=200, 
                verbose_eval=100, 
                T_focus=T_focus
            )
            return metrics["valid/ic_focus"]
        except Exception as e:
            logger.warning(f"Trial failed: {e}")
            return float("-inf") if direction == "maximize" else float("inf")

    # 创建Optuna study
    study = optuna.create_study(direction=direction, sampler=TPESampler(seed=seed))
    
    # 设置日志级别，减少输出
    optuna.logging.set_verbosity(optuna.logging.WARNING)
    
    # 优化
    study.optimize(objective, n_trials=n_trials)
    
    # 合并最优参数
    best_params = dict(base_params)
    best_params.update(study.best_params)
    
    logger.info(f"[Optuna] 最优IC={study.best_value:.6f}")
    logger.info(f"[Optuna] 最优参数={study.best_params}")
    
    return best_params

def save_params_json(params: Dict, path: str):
    """
    保存参数到JSON文件
    
    参数:
        params: 参数字典
        path: 保存路径
    """
    os.makedirs(os.path.dirname(path), exist_ok=True)
    with open(path, "w", encoding="utf-8") as f:
        json.dump(params, f, indent=2, ensure_ascii=False)
    logger.info(f"参数已保存到: {path}")