{"cells": [{"cell_type": "code", "execution_count": 9, "id": "e54b4538", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3209554,)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "import os\n", "checkpoint_path = \"/home/<USER>/tslib/projs/stock1m/checkpoints_tree\"\n", "\n", "test_pred = np.load(os.path.join(checkpoint_path, \"fold01\", \"test_pred.npy\"))\n", "\n", "test_pred.shape"]}, {"cell_type": "code", "execution_count": 10, "id": "db0a6d71", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3209554, 2)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "test_dt = np.load(os.path.join(checkpoint_path, \"fold01\", \"test_dt.npy\"))\n", "\n", "test_dt.shape"]}, {"cell_type": "code", "execution_count": 11, "id": "4a66b814", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[  0,   0],\n", "       [  0,   0],\n", "       [  0,   0],\n", "       ...,\n", "       [  2, 240],\n", "       [  2, 240],\n", "       [  2, 240]], dtype=int32)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["test_dt"]}, {"cell_type": "code", "execution_count": 4, "id": "9b77e32f", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[[0.07506309],\n", "        [0.08067583],\n", "        [0.08871107],\n", "        ...,\n", "        [0.12245415],\n", "        [0.12443129],\n", "        [0.12573229]],\n", "\n", "       [[0.06983919],\n", "        [0.07589951],\n", "        [0.08949044],\n", "        ...,\n", "        [0.12070292],\n", "        [0.12261236],\n", "        [0.12367194]],\n", "\n", "       [[0.02022516],\n", "        [0.00936001],\n", "        [0.0272888 ],\n", "        ...,\n", "        [0.11040882],\n", "        [0.11086893],\n", "        [0.11071897]],\n", "\n", "       ...,\n", "\n", "       [[0.0873569 ],\n", "        [0.1367887 ],\n", "        [0.17406702],\n", "        ...,\n", "        [0.22696516],\n", "        [0.23153725],\n", "        [0.23530388]],\n", "\n", "       [[0.14883968],\n", "        [0.10237581],\n", "        [0.10155713],\n", "        ...,\n", "        [0.19033825],\n", "        [0.19326949],\n", "        [0.19719823]],\n", "\n", "       [[0.08333711],\n", "        [0.09092355],\n", "        [0.09683087],\n", "        ...,\n", "        [0.10878555],\n", "        [0.10946617],\n", "        [0.11000152]]], dtype=float32)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["t2 = np.load(\"/home/<USER>/tslib/projs/stock1m/checkpoints/day_fold00/test_preds.npy\")\n", "t2"]}, {"cell_type": "code", "execution_count": 6, "id": "0ebbc8bd", "metadata": {}, "outputs": [{"data": {"text/plain": ["(8810, 241, 1)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["t2.shape"]}, {"cell_type": "code", "execution_count": null, "id": "638baf5c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "dl", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}